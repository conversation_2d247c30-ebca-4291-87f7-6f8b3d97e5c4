"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { RolesCombobox } from "@/components/ui/roles-combobox"
import { ClassesCombobox } from "@/components/ui/classes-combobox"
import { SortCombobox } from "@/components/ui/sort-combobox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, X } from "lucide-react"
import Image from "next/image"
import { getPositionIconUrl, getClassIconUrl } from "@/lib/utils/champion-data-utils"
import { RemoveFiltersIcon } from "@/components/ui/remove-filters-icon"

interface SearchPanelProps {
  isOpen: boolean
  onClose: () => void
  // Common filters
  searchTerm: string
  onSearchChange: (value: string) => void
  selectedRoles: string[]
  onRolesChange: (value: string[]) => void
  selectedClasses: string[]
  onClassesChange: (value: string[]) => void
  onResetFilters: () => void
  // Free rotation specific
  selectedRegion?: string
  onRegionChange?: (value: string) => void
  rotationType?: string
  onRotationTypeChange?: (value: string) => void
  regions?: Record<string, string>
  // Sorting (optional for free rotation)
  sortField?: 'name' | 'release' | 'price'
  sortDirection?: 'asc' | 'desc'
  onSortChange?: (field: 'name' | 'release' | 'price', direction: 'asc' | 'desc') => void
  // Role and class options
  roles: string[]
  classes: string[]
  // Pre-select role when opening (for role distribution clicks)
  preSelectRole?: string
  // Results count
  filteredCount?: number
  totalCount?: number
}

export default function SearchPanel({
  isOpen,
  onClose,
  searchTerm,
  onSearchChange,
  selectedRoles,
  onRolesChange,
  selectedClasses,
  onClassesChange,
  onResetFilters,
  selectedRegion,
  onRegionChange,
  rotationType,
  onRotationTypeChange,
  regions,
  sortField,
  sortDirection,
  onSortChange,
  roles,
  classes,
  preSelectRole,
  filteredCount,
  totalCount
}: SearchPanelProps) {


  // Local state for filters - don't apply until user clicks Apply
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm)
  const [localSelectedRoles, setLocalSelectedRoles] = useState(selectedRoles)
  const [localSelectedClasses, setLocalSelectedClasses] = useState(selectedClasses)
  const [localSelectedRegion, setLocalSelectedRegion] = useState(selectedRegion || '')
  const [localRotationType, setLocalRotationType] = useState(rotationType || 'all')
  const [localSortField, setLocalSortField] = useState(sortField || 'name')
  const [localSortDirection, setLocalSortDirection] = useState(sortDirection || 'asc')

  // Update local state when props change (e.g., when filters are reset externally)
  useEffect(() => {
    setLocalSearchTerm(searchTerm)
    setLocalSelectedRoles(selectedRoles)
    setLocalSelectedClasses(selectedClasses)
    setLocalSelectedRegion(selectedRegion || '')
    setLocalRotationType(rotationType || 'all')
    setLocalSortField(sortField || 'name')
    setLocalSortDirection(sortDirection || 'asc')
  }, [searchTerm, selectedRoles, selectedClasses, selectedRegion, rotationType, sortField, sortDirection])

  useEffect(() => {
    if (isOpen && preSelectRole && preSelectRole !== 'Any') {
      setLocalSelectedRoles([preSelectRole])
    }
  }, [isOpen, preSelectRole])

  const handleResetFilters = (e?: React.MouseEvent) => {
    // Prevent any default behavior that might cause scrolling
    if (e) {
      e.preventDefault()
      e.stopPropagation()
    }
    // Reset local state
    setLocalSearchTerm('')
    setLocalSelectedRoles([])
    setLocalSelectedClasses([])
    setLocalSelectedRegion('')
    setLocalRotationType('all')
    setLocalSortField('name')
    setLocalSortDirection('asc')
    // Apply reset to parent
    onResetFilters()
    // Close the search panel
    onClose()
  }

  const handleApplyFilters = () => {
    // Apply all local changes to parent state
    onSearchChange(localSearchTerm)
    onRolesChange(localSelectedRoles)
    onClassesChange(localSelectedClasses)
    if (onRegionChange) onRegionChange(localSelectedRegion)
    if (onRotationTypeChange) onRotationTypeChange(localRotationType)
    if (onSortChange) onSortChange(localSortField, localSortDirection)
    onClose()
  }



  return (
    <>
      {/* Mobile Only - Show only on small screens */}
      <div className="lg:hidden">
        {/* Backdrop */}
        <div
          className={`fixed inset-0 bg-black/50 z-40 transition-opacity duration-300 ${
            isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
          }`}
          onClick={onClose}
        />

        {/* Search Panel */}
        <div
          className={`fixed top-0 left-0 right-0 bg-gray-900/95 backdrop-blur-sm border-b border-orange-700/30 z-50 transition-all duration-300 ease-out transform ${
            isOpen ? 'translate-y-0 opacity-100' : '-translate-y-full opacity-0'
          }`}
        >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white flex items-center">
              <Search className="h-5 w-5 mr-2 text-orange-400" />
              Search & Filters
            </h3>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-gray-400 hover:text-white"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <Input
                type="text"
                placeholder="Search champions..."
                value={localSearchTerm}
                onChange={(e) => setLocalSearchTerm(e.target.value)}
                className="pl-12 bg-gray-800/50 border-gray-700/50 text-white placeholder-gray-400 focus:border-orange-400/60 h-12 text-lg"
              />
            </div>
          </div>

          {/* Filters - Full width layout to match search bar */}
          <div className="space-y-4 mb-6">
            {/* Region and Type filters (Free Rotation only) - Full width when present */}
            {(selectedRegion !== undefined || rotationType !== undefined) && (
              <div className="space-y-4 mb-6">
                {/* Region Filter (Free Rotation only) */}
                {selectedRegion !== undefined && onRegionChange && regions && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Region</label>
                    <Select value={localSelectedRegion} onValueChange={setLocalSelectedRegion}>
                      <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 text-white focus:border-orange-400/60 h-12">
                        <SelectValue placeholder="Select Region" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-900 border-gray-700 max-h-60 overflow-y-auto" position="popper" sideOffset={4}>
                        {Object.entries(regions).map(([displayName, regionCode]) => (
                          <SelectItem key={regionCode} value={regionCode} className="text-white hover:bg-orange-400/10">
                            {displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Rotation Type Filter (Free Rotation only) */}
                {rotationType !== undefined && onRotationTypeChange && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-300">Type</label>
                    <Select value={localRotationType} onValueChange={setLocalRotationType}>
                      <SelectTrigger className="w-full bg-gray-800/50 border-gray-700/50 text-white focus:border-orange-400/60 h-12">
                        <SelectValue placeholder="All Rotations" />
                      </SelectTrigger>
                      <SelectContent className="bg-gray-900 border-gray-700 max-h-60 overflow-y-auto" position="popper" sideOffset={4}>
                        <SelectItem value="all" className="text-white hover:bg-orange-400/10">
                          <div className="flex items-center space-x-2">
                            <span>All</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="new-player" className="text-white hover:bg-orange-400/10">
                          <div className="flex items-center space-x-2">
                            <span>New Player</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="free-rotation" className="text-white hover:bg-orange-400/10">
                          <div className="flex items-center space-x-2">
                            <span>Free Rotation</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>
            )}

            {/* Role and Class filters - Same row, full width of columns */}
            <div className="grid grid-cols-2 gap-2 w-full !m-0 !max-w-[auto]">
              {/* Role Filter */}
              <div className="space-y-1 w-full">
                <label className="text-sm font-medium text-gray-300">Roles</label>
                <RolesCombobox
                  selectedRoles={localSelectedRoles}
                  onSelectionChange={setLocalSelectedRoles}
                  availableRoles={roles}
                  placeholder="Select Roles"
                  className="w-full"
                />
              </div>

              {/* Class Filter */}
              <div className="space-y-1 w-full">
                <label className="text-sm font-medium text-gray-300">Classes</label>
                <ClassesCombobox
                  selectedClasses={localSelectedClasses}
                  onSelectionChange={setLocalSelectedClasses}
                  availableClasses={classes}
                  placeholder="Select Classes"
                  className="w-full"
                />
              </div>
            </div>

            {/* Sorting Filter (Free Rotation only) */}
            {sortField !== undefined && onSortChange && (
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-300">Sort By</label>
                <SortCombobox
                  selectedValue={`${localSortField}-${localSortDirection}`}
                  onSelectionChange={(value: string) => {
                    const [field, direction] = value.split('-') as [typeof localSortField, typeof localSortDirection]
                    setLocalSortField(field)
                    setLocalSortDirection(direction)
                  }}
                  options={[
                    { value: "name-asc", label: "Name (A–Z)" },
                    { value: "name-desc", label: "Name (Z–A)" },
                    { value: "release-asc", label: "Oldest First" },
                    { value: "release-desc", label: "Newest First" },
                    { value: "price-asc", label: "Price (Low to High)" },
                    { value: "price-desc", label: "Price (High to Low)" }
                  ]}
                  placeholder="Sort by..."
                  className="w-full"
                  hoverColor="orange-400/10"
                />
              </div>
            )}
          </div>

          {/* Results Count */}
          <div className="mb-6 p-3 bg-gray-800/30 rounded-lg border border-orange-700/20">
            <p className="text-sm text-gray-300">
              Showing <span className="text-orange-400 font-medium">{filteredCount || 0}</span> of{' '}
              <span className="text-gray-100 font-medium">{totalCount || 0}</span> champions
            </p>
          </div>

          {/* Action Buttons - Full width */}
          <div className="mt-8">
            <div className="flex gap-3">
              {/* Reset Filters Button */}
              <Button
                onClick={handleResetFilters}
                variant="outline"
                className="border-gray-700/30 text-gray-300 hover:bg-gray-800/50 hover:text-red-400 p-3"
                title="Reset all filters"
                type="button"
              >
                <RemoveFiltersIcon className="h-5 w-5" />
              </Button>

              {/* Apply Button */}
              <Button
                onClick={handleApplyFilters}
                className="flex-1 bg-gradient-to-r from-orange-600 to-amber-600 hover:from-orange-700 hover:to-amber-700"
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  )
}
