"use client"

import { useRef, useEffect, useState } from 'react'

interface UseDragScrollOptions {
  direction?: 'horizontal' | 'vertical'
  sensitivity?: number
}

export function useDragScroll({
  direction = 'horizontal',
  sensitivity = 0.8
}: UseDragScrollOptions = {}) {
  const scrollRef = useRef<HTMLDivElement>(null)
  const animationIdRef = useRef<number | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [startPos, setStartPos] = useState({ x: 0, y: 0 })
  const [scrollStart, setScrollStart] = useState({ x: 0, y: 0 })

  useEffect(() => {
    const element = scrollRef.current
    if (!element) return

    // animationId is now stored in animationIdRef

    const handleMouseDown = (e: MouseEvent) => {
      setIsDragging(true)
      setStartPos({ x: e.clientX, y: e.clientY })
      setScrollStart({ 
        x: element.scrollLeft, 
        y: element.scrollTop 
      })
      element.style.cursor = 'grabbing'
      element.style.userSelect = 'none'
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return

      e.preventDefault()

      const deltaX = (e.clientX - startPos.x) * sensitivity
      const deltaY = (e.clientY - startPos.y) * sensitivity

      if (direction === 'horizontal') {
        const newScrollLeft = scrollStart.x - deltaX
        element.scrollLeft = Math.max(0, Math.min(newScrollLeft, element.scrollWidth - element.clientWidth))
      } else {
        const newScrollTop = scrollStart.y - deltaY
        element.scrollTop = Math.max(0, Math.min(newScrollTop, element.scrollHeight - element.clientHeight))
      }
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      element.style.cursor = 'grab'
      element.style.userSelect = 'auto'
    }

    // Touch events for mobile
    const handleTouchStart = (e: TouchEvent) => {
      const touch = e.touches[0]
      setIsDragging(true)
      setStartPos({ x: touch.clientX, y: touch.clientY })
      setScrollStart({ 
        x: element.scrollLeft, 
        y: element.scrollTop 
      })
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging) return

      const touch = e.touches[0]
      const deltaX = (touch.clientX - startPos.x) * sensitivity
      const deltaY = (touch.clientY - startPos.y) * sensitivity

      if (direction === 'horizontal') {
        const newScrollLeft = scrollStart.x - deltaX
        element.scrollLeft = Math.max(0, Math.min(newScrollLeft, element.scrollWidth - element.clientWidth))
      } else {
        const newScrollTop = scrollStart.y - deltaY
        element.scrollTop = Math.max(0, Math.min(newScrollTop, element.scrollHeight - element.clientHeight))
      }
    }

    const handleTouchEnd = () => {
      setIsDragging(false)
    }

    // Mouse events
    element.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    // Touch events
    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchmove', handleTouchMove, { passive: true })
    element.addEventListener('touchend', handleTouchEnd)

    // Prevent default drag behavior on images and links
    element.addEventListener('dragstart', (e) => e.preventDefault())

    // Set initial cursor
    element.style.cursor = 'grab'

    return () => {
      element.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
      element.removeEventListener('dragstart', (e) => e.preventDefault())
      
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
    }
  }, [isDragging, startPos, scrollStart, direction, sensitivity])

  return {
    scrollRef,
    isDragging
  }
}
